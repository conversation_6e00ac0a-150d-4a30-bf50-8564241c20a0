const express = require('express');
const cron = require('node-cron');
const nodemailer = require('nodemailer');
require('dotenv').config();

const app = express();
const PORT = 3178;

// Email configuration
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS
  },
  pool: true,
  maxConnections: 1,
  maxMessages: 3
});

// Function to send email
async function sendReminderEmail(bookings = []) {

  // Sort bookings by check-in date and filter for future dates only
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Set to start of today
  
  const sortedBookings = bookings
    .filter(booking => {
      // Filter for future dates only
      const getCheckinDate = (booking) => {
        try {
          const parsed = JSON.parse(booking.metadata || '{}');
          const responses = parsed.responses || {};
          
          const checkin = parsed.checkin || 
                         responses.checkin?.vote || 
                         parsed.checkingDate || 
                         responses.checkingDate?.vote || 
                         responses.checkinDate?.vote;
          
          if (!checkin) return null;
          
          const date = new Date(checkin);
          return isNaN(date.getTime()) ? null : date;
        } catch (e) {
          return null;
        }
      };
      
      const checkinDate = getCheckinDate(booking);
      return checkinDate && checkinDate >= today; // Only future dates
    })
    .sort((a, b) => {
      // Extract check-in dates for sorting
      const getCheckinDate = (booking) => {
        try {
          const parsed = JSON.parse(booking.metadata || '{}');
          const responses = parsed.responses || {};
          
          const checkin = parsed.checkin || 
                         responses.checkin?.vote || 
                         parsed.checkingDate || 
                         responses.checkingDate?.vote || 
                         responses.checkinDate?.vote;
          
          if (!checkin) return null;
          
          const date = new Date(checkin);
          return isNaN(date.getTime()) ? null : date;
        } catch (e) {
          return null;
        }
      };
      
      const dateA = getCheckinDate(a);
      const dateB = getCheckinDate(b);
      
      // Sort by date (ascending - earliest first)
      return dateA - dateB;
    })
    .slice(0, 20); // Get only the first 20 bookings

  const bookingsTable = sortedBookings.length > 0 
    ? sortedBookings.map(booking => {
        let parsedData = {};
        try {
          parsedData = JSON.parse(booking.metadata || '{}');
        } catch (e) {
          parsedData = {};
        }
        
        const responses = parsedData.responses || {};
        const email = parsedData.email?.vote || responses.bookingrecipient?.vote || 'N/A';
        const guestName = responses.guest?.vote || 'N/A';
        const hotel = responses.hotel?.vote || 'N/A';
        const roomType = responses.roomtype?.vote || 'N/A';
        const checkingDateRaw = parsedData.checkin || responses.checkin?.vote || parsedData.checkingDate || responses.checkingDate?.vote || responses.checkinDate?.vote;
        const checkingDate = checkingDateRaw && checkingDateRaw !== 'N/A' ? new Date(checkingDateRaw) : null;
        
        return `
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">${booking.id || 'N/A'}</td>
            <td style="border: 1px solid #ddd; padding: 8px;">${email}</td>
            <td style="border: 1px solid #ddd; padding: 8px;">${guestName}</td>
            <td style="border: 1px solid #ddd; padding: 8px;">${hotel}</td>
            <td style="border: 1px solid #ddd; padding: 8px;">${roomType}</td>
            <td style="border: 1px solid #ddd; padding: 8px;">${checkingDate ? checkingDate.toLocaleDateString() : 'N/A'}</td>
          </tr>
        `;
      }).join('')
    : '<tr><td colspan="6" style="border: 1px solid #ddd; padding: 8px; text-align: center;">No pending bookings found</td></tr>';

  // Get recipient email from environment variable
  const recipientEmail = process.env.RECIPIENT_EMAIL || '<EMAIL>';

  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: recipientEmail,
    subject: `Pending Bookings (${bookings.length})`,
    html: `
      <h2>${bookings.length} Pending Bookings</h2>
      <p>Hi there,</p>
      <p>Here are your pending bookings:</p>
      <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
        <thead>
          <tr style="background-color: #f2f2f2;">
            <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Booking ID</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Email</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Guest Name</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Villa/Hotel</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Room Type</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Check In Date</th>
          </tr>
        </thead>
        <tbody>
          ${bookingsTable}
        </tbody>
      </table>
      <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
      <br>
      <p>Stay productive! </p>
    `
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log(`Reminder email sent with ${bookings.length} bookings at ${new Date().toLocaleString()}`);
  } catch (error) {
    console.error('Failed to send email:', error.message);
  }
}

// Function to get auth token
async function getAuthToken() {
  try {
    const crowdsnapResponse = await fetch(`${process.env.CROWDSNAP_DOMAIN}/api/client/auth`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: process.env.CROWDSNAP_EMAIL, 
        password: process.env.CROWDSNAP_PASSWORD,
        origin: "insightx"
      }),
    });

    const crowdsnapData = await crowdsnapResponse.json();
    console.log('Token:', crowdsnapData.token);
    return crowdsnapData.token;

  } catch (error) {
    console.error('Failed to get auth token:', error.message);
    return null;
  }
}

// Function to get pending bookings
async function getPendingBookings(token) {
  try {
    const bookingsResponse = await fetch(`${process.env.CROWDSNAP_API_DOMAIN}/api/polls/getbookingsfromQ`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify({
        eventid: process.env.EVENT_ID,
        subtype: "process",
        status: "pending"
      }),
    });

    const bookingsData = await bookingsResponse.json();
    console.log(`pending bookings`,bookingsData);
    return bookingsData;
  } catch (error) {
    console.error('Failed to get bookings:', error.message);
    return [];
  }
}

// Main cron job function
async function checkBookingsAndSendEmail() {
  console.log('Starting booking check...');
  
  // Step 1: Get auth token
  const token = await getAuthToken();
  console.log('Token2:', token);

  if (!token) {
    console.error('Cannot proceed without auth token');
    return;
  }

  // Step 2: Get pending bookings
  const bookings = await getPendingBookings(token);
  
  // Step 3: Send email with bookings
  if (bookings && bookings.length > 0) {
    await sendReminderEmail(bookings); // Send all bookings in one email
    console.log(`Single email sent with ${bookings.length} bookings`);
  } else {
    console.log('No pending bookings found - no emails sent');
  }
}

// Cron job - every 3 minutes
cron.schedule('*/3 * * * *', () => {
  console.log('Cron job started at:', new Date().toLocaleString());
  checkBookingsAndSendEmail();
});

// Manual trigger endpoint for testing
app.get('/test-booking', async (req, res) => {
  await checkBookingsAndSendEmail();
  res.json({ message: 'Booking check triggered manually' });
});

app.listen(PORT, () => {
  console.log(`Manual test: http://localhost:${PORT}/test-booking`);
});
